-- 各市场区域昨日库存报表 - 按库存天数区间统计
-- 直接使用dbo.ODS_T5_HJLY_VCPKC_CCZS表的Dept_Name字段
SELECT
    -- 东部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS
            WHERE Dept_Name = '东部大区'), 0) AS 东部大区_昨天_库存,

    -- 西部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS
            WHERE Dept_Name = '西部大区'), 0) AS 西部大区_昨天_库存,

    -- 南部大区数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS
            WHERE Dept_Name = '南部大区'), 0) AS 南部大区_昨天_库存,

    -- 海外事业发展数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS
            WHERE Dept_Name = '海外事业发展'), 0) AS 海外事业发展_昨天_库存,

    -- 新材各区域及部门汇总数据
    ISNULL((SELECT COUNT(DISTINCT dzid) FROM dbo.ODS_T5_HJLY_VCPKC_CCZS
            WHERE Dept_Name IN ('新材北部区域', '新材中部区域', '新材华南区域', '新材华中区域', '新材料业务部', '新材山东区域', '新材西部区域')), 0) AS 新材各区域及部门_昨天_库存